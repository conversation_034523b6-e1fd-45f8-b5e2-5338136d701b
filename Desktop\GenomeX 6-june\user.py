import logging
import os
import re
import ast
import subprocess
import atexit
import time,sys
from pathlib import Path
from new_ai import gemini

# Get the current working directory to ensure files are saved in the right place
CURRENT_DIR = os.getcwd()
print(f"Working directory: {CURRENT_DIR}")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Cache for gemini responses
GEMINI_CACHE = {}

def cleanup_temp_file():
    """Clean up temporary files."""
    temp_file_path = os.path.join(CURRENT_DIR, "temp.py")
    if os.path.exists(temp_file_path):
        try:
            os.remove(temp_file_path)
            logging.info("Cleaned up temp.py")
        except Exception as e:
            logging.warning(f"Could not clean up temp.py: {e}")

atexit.register(cleanup_temp_file)

def get_user_request() -> str:
    """Prompts user for project idea."""
    request = input("Enter what you want to build: ").strip()
    if not request:
        logging.error("No project idea provided.")
        raise ValueError("Project idea cannot be empty.")
    return request

def safe_gemini(prompt: str, max_retries: int = 3, use_web_search: bool = False) -> str:
    """Call gemini with retries and caching."""
    cache_key = f"{prompt}_{use_web_search}"
    if cache_key in GEMINI_CACHE:
        logging.debug("Using cached gemini response.")
        return GEMINI_CACHE[cache_key]

    code_regex = r"```(?:[a-zA-Z0-9]+\n)?([\s\S]*?)```" # Define the regex

    for attempt in range(max_retries):
        try:
            response = gemini(prompt, use_web_search=use_web_search).strip()
            logging.debug(f"gemini prompt:\n{prompt}\nResponse:\n{response}")

            # Extract code from markdown block
            match = re.search(code_regex, response)
            if match:
                extracted_code = match.group(1).strip()
                logging.debug(f"Extracted code:\n{extracted_code}")
                GEMINI_CACHE[cache_key] = extracted_code # Cache extracted code
                return extracted_code # Return extracted code
            else:
                logging.warning("No markdown code block found in gemini response.")
                GEMINI_CACHE[cache_key] = response # Cache original response if no block found
                return response # Return original response if no block found

        except Exception as e:
            logging.warning(f"gemini failed on attempt {attempt + 1}: {e}")
            if attempt == max_retries - 1:
                logging.error("gemini failed after all retries.")
                return None
    return None

def generate_filename(project_idea: str) -> str:
    """Generates a valid, readable filename."""
    prompt = f"Suggest a one or two-word filename (lowercase, no spaces or special characters except hyphens, readable) for a project that involves: {project_idea}. Respond with ONLY the filename."
    filename = safe_gemini(prompt)
    if filename and re.match(r"^[a-z0-9_-]{1,50}$", filename) and not filename.startswith("-"):
        return filename
    
    # Fallback: Derive filename from project idea
    words = re.sub(r"[^a-z0-9\s-]", "", project_idea.lower()).split()
    filename = "-".join(words[:2]) if len(words) >= 2 else words[0] if words else "default_project"
    logging.warning(f"Invalid filename from gemini. Using derived: {filename}")
    return filename

def is_valid_python_code(code: str) -> bool:
    """Validates Python code syntax."""
    try:
        ast.parse(code)
        return True
    except SyntaxError as e:
        logging.warning(f"Invalid Python code: {e}\nCode:\n{code}")
        return False

def sanitize_code(code: str) -> str:
    """Sanitizes code to remove dangerous constructs."""
    dangerous_patterns = [
        r"os\.system\(",
        r"subprocess\.run\(",
        r"exec\(",
        r"eval\(",
        r"__import__\("
    ]
    for pattern in dangerous_patterns:
        if re.search(pattern, code):
            logging.warning(f"Removed dangerous construct matching {pattern}")
            code = re.sub(pattern, "# Removed dangerous call: ", code)
    return code

def create_goal_file(filename: str, project_idea: str) -> str:
    """Creates a goal file with high-level workflow."""
    prompt = f"""
    You are an expert software architect.
    Create a goal and workflow for a Python project based on the idea: {project_idea}.
    The goal must be a concise, one-sentence description of the project's purpose.
    The workflow must have exactly 5-7 high-level, sequential steps that break down the program's execution flow in a clear, logical order (e.g., 'first do this, then that').
    Each step must start with '- ' and describe a single, abstract phase of the program, suitable for an LLM to generate code for.
    Use EXACTLY two headers: '#Goal' and '#Workflow'.
    Do NOT use 'Step X', 'Phase X', or any other labels in the workflow steps.
    Steps must reflect the program's logical flow, not setup or testing tasks, unless specified.
    Examples:
    For 'evolve a Python script using AST mutations':
    #Goal: Automatically evolve a Python script by mutating its AST and selecting better-performing variants.
    #Workflow:
    - Load the original Python script (main.py) and parse it into an AST.
    - Generate N mutated versions of the AST using random transformations.
    - Convert each mutated AST back to source code.
    - Run each mutated version and evaluate its performance based on a custom fitness function.
    - Select the top-performing mutation(s) and repeat the process for a fixed number of generations.
    For 'terminal-based Tic-Tac-Toe (2 player)':
    #Goal: Create a terminal-based two-player Tic-Tac-Toe game.
    #Workflow:
    - Initialize a 3x3 game board to track player moves.
    - Display the current state of the board in the terminal.
    - Prompt the current player for their move and validate the input.
    - Update the board with the player’s move.
    - Check for a win or draw condition after each move.
    - Alternate between players until the game ends.
    Output format:
    #Goal: [Goal]
    #Workflow:
    - [Description]
    - [Description]
    - [Description]
    - [Description]
    - [Description]
    """
    generated_content = safe_gemini(prompt)

    default_content = f"""
#Goal: Implement a Python program for {project_idea}.
#Workflow:
- Initialize the core data structures for the program.
- Set up the main program loop or logic.
- Handle user input or data processing.
- Update the program state based on input.
- Check for completion or termination conditions.
- Display the results or output to the user.
"""

    content_to_write = default_content.strip()
    goal_and_workflow = default_content.strip() # Initialize with default content

    # Validate format and select content if generated
    if generated_content and "#Goal:" in generated_content and "#Workflow:" in generated_content:
        logging.debug("Generated content contains #Goal and #Workflow headers.")
        print("Generated goal and workflow:")
        print(generated_content)
        if input("Approve this workflow? (y/n): ").lower() == 'y':
            logging.info("User approved generated workflow.")
            content_to_write = generated_content.strip() # Use approved generated content
            goal_and_workflow = generated_content.strip()
        else:
            logging.info("User rejected generated workflow. Using default.")
            # content_to_write and goal_and_workflow remain default
    else:
        logging.warning("Generated content is invalid or missing headers. Using default.\nGenerated content:\n{generated_content}")
        # content_to_write and goal_and_workflow remain default

    try:
        goal_file = f"{filename}_goal.txt"
        goal_file_path = os.path.join(CURRENT_DIR, goal_file)

        print(f"Creating goal file: {goal_file_path}")

        if os.path.exists(goal_file_path):
            backup_goal_file(goal_file_path)

        with open(goal_file_path, "w", encoding="utf-8") as f:
            f.write(content_to_write)

        # Verify the file was created
        if os.path.exists(goal_file_path):
            file_size = os.path.getsize(goal_file_path)
            print(f"[OK] Goal file created successfully: {goal_file} ({file_size} bytes)")
            logging.info(f"Goal file '{goal_file}' created successfully at {goal_file_path}")
        else:
            print(f"[ERROR] Failed to create goal file: {goal_file}")
            logging.error(f"Goal file was not created at {goal_file_path}")
            return None

        return goal_and_workflow # Return the content that was actually written
    except Exception as e:
        print(f"[ERROR] Error creating goal file: {e}")
        logging.error(f"Error creating goal file: {e}")
        return None

def backup_goal_file(goal_file: str):
    """Creates a backup of the goal file."""
    try:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_file = f"{goal_file}.{timestamp}.bak"
        os.rename(goal_file, backup_file)
        print(f"[BACKUP] Backed up existing goal file to: {os.path.basename(backup_file)}")
        logging.info(f"Backed up {goal_file} to {backup_file}")
    except Exception as e:
        print(f"[WARNING] Could not backup goal file: {e}")
        logging.warning(f"Could not backup goal file: {e}")

def generate_code_for_step(goal_and_workflow: str, step: str) -> str:
    """Generates Python code for a workflow step."""
    prompt = f"""
    You are an expert Python developer.
    Given the following goal and workflow, write Python code for the following step:
    {goal_and_workflow}
    Step: {step}

    IMPORTANT GUIDELINES:
    - Write functions that can be called programmatically, not interactive scripts
    - Avoid using input() statements in the main execution flow
    - If user input is needed, create functions that accept parameters instead
    - Focus on creating reusable, modular functions
    - Include example usage in comments, but don't execute interactive code in the main block
    - Ensure the code can run without user interaction for testing purposes

    Output ONLY valid Python code, nothing else.
    Ensure the code is a logical part of the overall program and can be combined with other steps.
    """

    # Use web search for better code generation
    code = safe_gemini(prompt, use_web_search=True)
    if not code:
        logging.error(f"Failed to generate valid code for step: {step}")
        return None
    return sanitize_code(code)
def run_code(filename: str) -> tuple[bool, str]:
    """Runs Python code and returns success status and output/error."""
    try:
        result = subprocess.run([sys.executable, filename], capture_output=True, text=True, check=True, timeout=30)
        logging.info(f"Code ran successfully:\n{result.stdout}")
        return True, result.stdout
    except subprocess.TimeoutExpired:
        logging.error("Code execution timed out after 30 seconds.")
        return False, "Timeout expired"
    except subprocess.CalledProcessError as e:
        logging.error(f"Error running code:\n{e.stderr}")
        return False, e.stderr
    except Exception as e:
        logging.error(f"Unknown error: {e}")
        return False, str(e)

def fix_code_with_gemini(code: str, error_message: str, goal_and_workflow: str) -> str:
    """Fixes code based on error message."""
    prompt = f"""
    You are an expert Python developer.
    The following Python code produced an error:
    ```python
    {code}
    ```
    Error message:
    {error_message}
    Goal and workflow:
    {goal_and_workflow}
    Fix the code. Output ONLY valid Python code, nothing else.
    Ensure the fixed code aligns with the workflow step and can be combined with other steps.
    """
    fixed_code = safe_gemini(prompt)
    if not fixed_code or not is_valid_python_code(fixed_code):
        logging.error(f"Failed to fix code:\n{fixed_code}")
        return None
    return sanitize_code(fixed_code)

def backup_script(script_file: str):
    """Creates a backup of the script."""
    try:
        if os.path.exists(script_file):
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_file = f"{script_file}.{timestamp}.bak"
            os.rename(script_file, backup_file)
            print(f"[BACKUP] Backed up existing script to: {os.path.basename(backup_file)}")
            logging.info(f"Backed up {script_file} to {backup_file}")
    except Exception as e:
        print(f"[WARNING] Could not backup script file: {e}")
        logging.warning(f"Could not backup script file: {e}")

def main():
    """Orchestrates project creation."""
    try:
        project_idea = get_user_request()
        filename = generate_filename(project_idea)
        goal_and_workflow = create_goal_file(filename, project_idea)
        if not goal_and_workflow:
            print("Failed to create goal file. Exiting.")
            return

        workflow_steps = [line.strip()[2:] for line in goal_and_workflow.splitlines() if line.startswith("- ")]
        if len(workflow_steps) < 5:
            logging.error(f"Too few workflow steps: {len(workflow_steps)}")
            print("Too few workflow steps found. Exiting.")
            return

        final_code = "# Generated Python script\n\n"
        script_file = f"{filename}.py"
        script_file_path = os.path.join(CURRENT_DIR, script_file)

        print(f"Will create Python script: {script_file_path}")

        for i, step in enumerate(workflow_steps):
            logging.info(f"Generating code for step {i+1}: {step}")
            code = generate_code_for_step(goal_and_workflow, step)
            if not code:
                print(f"Failed to generate code for step {i+1}. Exiting.")
                return

            final_code += f"# Step {i+1}: {step}\n{code}\n\n"

            # Skip code execution to avoid timeout issues with input() statements
            logging.info(f"Step {i+1} code added successfully (execution skipped)")
            print(f"[OK] Step {i+1} completed: {step}")

            # Optional: Save intermediate version for debugging
            temp_file_path = os.path.join(CURRENT_DIR, "temp.py")
            with open(temp_file_path, "w", encoding="utf-8") as f:
                f.write(final_code)

        # Validate final code before saving
        print("\nValidating final code...")
        if is_valid_python_code(final_code):
            print("[OK] Code validation successful!")
        else:
            print("[WARNING] Generated code may have syntax issues")

        # Save final script
        print(f"\nSaving final script to: {script_file_path}")

        if os.path.exists(script_file_path):
            backup_script(script_file_path)

        try:
            with open(script_file_path, "w", encoding="utf-8") as f:
                f.write(final_code)

            # Verify the file was created
            if os.path.exists(script_file_path):
                file_size = os.path.getsize(script_file_path)
                print(f"[OK] Script file created successfully!")

                # Show summary
                print(f"\n[SUCCESS] Project '{script_file}' created successfully!")
                print(f"File location: {script_file_path}")
                print(f"File size: {file_size} bytes ({len(final_code)} characters)")
                print(f"Steps completed: {len(workflow_steps)}")
                print(f"To run your project: python {script_file}")

                logging.info(f"Project '{script_file}' created successfully at {script_file_path}")

                # List files in current directory for verification
                print(f"\nFiles in current directory ({CURRENT_DIR}):")
                try:
                    files = [f for f in os.listdir(CURRENT_DIR) if f.endswith(('.py', '.txt'))]
                    for file in sorted(files):
                        file_path = os.path.join(CURRENT_DIR, file)
                        size = os.path.getsize(file_path)
                        print(f"   {file} ({size} bytes)")
                except Exception as e:
                    print(f"   [ERROR] Could not list files: {e}")

            else:
                print(f"[ERROR] Failed to create script file: {script_file}")
                logging.error(f"Script file was not created at {script_file_path}")

        except Exception as e:
            print(f"[ERROR] Error saving script file: {e}")
            logging.error(f"Error saving script file: {e}")
            raise

    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        print(f"An error occurred: {e}. Exiting.")

if __name__ == "__main__":
    main()


# the problem this this is it is generating the workflow 
# but has lot's of print statments while debugging and all
# also the regex while genetating the code is so strict that even it is not allowing the code to be valid,
# i want a regex only to veriry and get the code part of the LLM response at rhe output
