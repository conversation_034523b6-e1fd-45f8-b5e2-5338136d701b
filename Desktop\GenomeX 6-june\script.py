import user
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    """
    Runs the main project creation process defined in user.py.
    """
    logging.info("Starting project creation process via user.py")
    try:
        user.main()
        logging.info("Project creation process completed.")
    except Exception as e:
        logging.error(f"An error occurred during the project creation process: {e}")

if __name__ == "__main__":
    main()
