import google.generativeai as genai
import requests
import json
import time
import random
from typing import Optional, List, Dict, Any

# Configure Gemini API
genai.configure(api_key="AIzaSyDzVcofu-epzmPwqqGugQeT7Hox5G2Qk1k")
model = genai.GenerativeModel("models/gemma-3-27b-it")

# Web search configuration
SERPER_API_KEY = "YOUR_SERPER_API_KEY"  # Replace with your actual API key
SEARCH_ENABLED = True  # Set to False to disable web search globally

def web_search(query: str, num_results: int = 3) -> List[Dict[str, Any]]:
    """
    Perform a web search using the Serper API.

    Args:
        query: The search query
        num_results: Number of results to return

    Returns:
        List of search results with title, link, and snippet
    """
    if not SEARCH_ENABLED or not SERPER_API_KEY or SERPER_API_KEY == "YOUR_SERPER_API_KEY":
        return []

    try:
        url = "https://google.serper.dev/search"
        payload = json.dumps({
            "q": query,
            "num": num_results
        })
        headers = {
            'X-API-KEY': SERPER_API_KEY,
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            results = []

            # Process organic results
            if "organic" in data:
                for item in data["organic"][:num_results]:
                    results.append({
                        "title": item.get("title", ""),
                        "link": item.get("link", ""),
                        "snippet": item.get("snippet", "")
                    })

            return results
        return []
    except Exception as e:
        print(f"Web search error: {str(e)}")
        return []

def gemini(prompt: str, use_web_search: bool = False, search_query: Optional[str] = None) -> str:
    """
    Generate content using Gemini model with optional web search enhancement.

    Args:
        prompt: The prompt to send to the model
        use_web_search: Whether to enhance the prompt with web search results
        search_query: Custom search query (if None, will use the prompt)

    Returns:
        Generated text response
    """
    enhanced_prompt = prompt

    # Add randomization factor to ensure different responses
    timestamp = int(time.time())
    random_seed = random.randint(1000, 9999)

    if use_web_search and SEARCH_ENABLED:
        query = search_query if search_query else prompt[:100]
        search_results = web_search(query)

        if search_results:
            search_context = "\n\nRelevant information from web search:\n"
            for i, result in enumerate(search_results, 1):
                search_context += f"{i}. {result['title']}\n"
                search_context += f"   URL: {result['link']}\n"
                search_context += f"   {result['snippet']}\n\n"

            enhanced_prompt = f"{search_context}\n\n{prompt}"

    # Add a hidden seed to ensure different responses even for identical prompts
    enhanced_prompt += f"\n\n[Internal: Use creativity seed {timestamp}-{random_seed} for unique response]"

    try:
        response = model.generate_content(enhanced_prompt)
        return response.text
    except Exception as e:
        print(f"Gemini API error: {str(e)}")
        # Retry once with a simpler prompt if there's an error
        try:
            simplified_prompt = prompt[:500] + f"\n\n[Internal: Use creativity seed {timestamp}-{random_seed+1} for unique response]"
            response = model.generate_content(simplified_prompt)
            return response.text
        except:
            return f"Error generating response: {str(e)}"

