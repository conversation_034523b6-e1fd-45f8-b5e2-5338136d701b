import logging
import os
import re
import ast
import atexit
import time,sys
from pathlib import Path
from new_ai import gemini

# Get the current working directory to ensure files are saved in the right place
CURRENT_DIR = os.getcwd()
logging.info(f"Working directory: {CURRENT_DIR}")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Cache for gemini responses
GEMINI_CACHE = {}

def cleanuptempfile():
    """Clean up temporary files."""
    tempfilepath = os.path.join(CURRENT_DIR, "temp.py")
    if os.path.exists(tempfilepath):
        try:
            os.remove(tempfilepath)
            logging.info("Cleaned up temp.py")
        except Exception as e:
            logging.warning(f"Could not clean up temp.py: {e}")

atexit.register(cleanuptempfile)

def getuserrequest() -> str:
    """Prompts user for project idea."""
    request = input("Enter what you want to build: ").strip()
    if not request:
        logging.error("No project idea provided.")
        raise ValueError("Project idea cannot be empty.")
    return request

def safegemini(prompt: str, maxretries: int = 3) -> str:
    """Call gemini with retries and caching."""
    cachekey = prompt
    if cachekey in GEMINI_CACHE:
        logging.debug("Using cached gemini response.")
        return GEMINI_CACHE[cachekey]

    code_regex = r"```python([\s\S]*?)```"  # Define the regex

    for attempt in range(maxretries):
        try:
            response = gemini(prompt).strip()
            logging.debug(f"gemini prompt:\n{prompt}\nResponse:\n{response}")

            # Extract code from markdown block
            match = re.search(code_regex, response)
            if match:
                extracted_code = match.group(1).strip()
                logging.debug(f"Extracted code:\n{extracted_code}")
                GEMINI_CACHE[cachekey] = extracted_code # Cache extracted code
                return extracted_code # Return extracted code
            else:
                logging.warning("No markdown code block found in gemini response.")
                GEMINI_CACHE[cachekey] = response # Cache original response if no block found
                return response # Return original response if no block found

        except Exception as e:
            logging.warning(f"gemini failed on attempt {attempt + 1}: {e}")
            if attempt == maxretries - 1:
                logging.error("gemini failed after all retries.")
                return None
    return None

def generatefilename(projectidea: str) -> str:
    """Generates a valid, readable filename."""
    prompt = f"Suggest a one or two-word filename (lowercase, no spaces or special characters except hyphens, readable) for a project that involves: {projectidea}. Respond with ONLY the filename."
    filename = safegemini(prompt)
    if filename and re.match(r"^[a-z0-9_-]{1,50}$", filename) and not filename.startswith("-"):
        return filename

    # Fallback: Derive filename from project idea
    words = re.sub(r"[^a-z0-9\s-]", "", projectidea.lower()).split()
    filename = "-".join(words[:2]) if len(words) >= 2 else words[0] if words else "default_project"
    logging.warning(f"Invalid filename from gemini. Using derived: {filename}")
    return filename

def isvalidpythoncode(code: str) -> bool:
    """Validates Python code syntax."""
    try:
        ast.parse(code)
        return True
    except SyntaxError as e:
        logging.warning(f"Invalid Python code: {e}\nCode:\n{code}")
        return False

def sanitize_code(code: str) -> str:
    """Sanitizes code to remove dangerous constructs."""
    dangerous_patterns = [
        r"os\.system\(",
        r"subprocess\.run\(",
        r"exec\(",
        r"eval\(",
        r"_import_\("
    ]
    for pattern in dangerous_patterns:
        if re.search(pattern, code):
            logging.warning(f"Removed dangerous construct matching {pattern}")
            code = re.sub(pattern, "# Removed dangerous call: ", code)
    return code

def creategoalfile(filename: str, project_idea: str) -> str:
    """Creates a goal file with high-level workflow."""
    prompt = f"""
    You are an expert software architect.
    Create a goal and workflow for a Python project based on the idea: {project_idea}.
    The goal must be a concise, one-sentence description of the project's purpose.
    The workflow must have exactly 5-7 high-level, sequential steps that break down the program's execution flow in a clear, logical order (e.g., 'first do this, then that').
    Each step must start with '- ' and describe a single, abstract phase of the program, suitable for an LLM to generate code for.
    Use EXACTLY two headers: '#Goal' and '#Workflow'.
    Do NOT use 'Step X', 'Phase X', or any other labels in the workflow steps.
    Steps must reflect the program's logical flow, not setup or testing tasks, unless specified.
    Examples:
    For 'evolve a Python script using AST mutations':
    #Goal: Automatically evolve a Python script by mutating its AST and selecting better-performing variants.
    #Workflow:

Load the original Python script (main.py) and parse it into an AST.
Generate N mutated versions of the AST using random transformations.
Convert each mutated AST back to source code.
Run each mutated version and evaluate its performance based on a custom fitness function.
Select the top-performing mutation(s) and repeat the process for a fixed number of generations.

    For 'terminal-based Tic-Tac-Toe (2 player)':
    #Goal: Create a terminal-based two-player Tic-Tac-Toe game.
    #Workflow:

Initialize a 3x3 game board to track player moves.
Display the current state of the board in the terminal.
Prompt the current player for their move and validate the input.
Update the board with the player’s move.
Check for a win or draw condition after each move.
Alternate between players until the game ends.

    Output format:
    #Goal: [Goal]
    #Workflow:

[Description]
[Description]
[Description]
[Description]
[Description]

    """
    generatedcontent = safegemini(prompt)

    default_content = f"""
#Goal: Implement a Python program for {project_idea}.
#Workflow:

Initialize the core data structures for the program.
Set up the main program loop or logic.
Handle user input or data processing.
Update the program state based on input.
Check for completion or termination conditions.
Display the results or output to the user.

"""

    contenttowrite = default_content.strip()
    goalandworkflow = default_content.strip() # Initialize with default content

    # Validate format and select content if generated
    if generatedcontent and "#Goal:" in generatedcontent and "#Workflow:" in generatedcontent:
        logging.debug("Generated content contains #Goal and #Workflow headers.")
        print("Generated goal and workflow:")
        print(generatedcontent)
        if input("Approve this workflow? (y/n): ").lower() == 'y':
            logging.info("User approved generated workflow.")
            contenttowrite = generatedcontent.strip() # Use approved generated content
            goalandworkflow = generatedcontent.strip()
        else:
            logging.info("User rejected generated workflow. Using default.")
            # contenttowrite and goalandworkflow remain default
    else:
        logging.warning(f"Generated content is invalid or missing headers. Using default.\nGenerated content:\n{generatedcontent}")
        # contenttowrite and goalandworkflow remain default

    try:
        goalfile = f"{filename}goal.txt"
        goalfilepath = os.path.join(CURRENT_DIR, goalfile)

        print(f"Creating goal file: {goalfilepath}")

        if os.path.exists(goalfilepath):
            backupgoalfile(goalfilepath)

        with open(goalfilepath, "w", encoding="utf-8") as f:
            f.write(contenttowrite)

        # Verify the file was created
        if os.path.exists(goalfilepath):
            filesize = os.path.getsize(goalfilepath)
            print(f"[OK] Goal file created successfully: {goalfile} ({filesize} bytes)")
            logging.info(f"Goal file '{goalfile}' created successfully at {goalfilepath}")
        else:
            print(f"[ERROR] Failed to create goal file: {goalfile}")
            logging.error(f"Goal file was not created at {goalfilepath}")
            return None

        return goalandworkflow # Return the content that was actually written
    except Exception as e:
        print(f"[ERROR] Error creating goal file: {e}")
        logging.error(f"Error creating goal file: {e}")
        return None

def backupgoalfile(goalfile: str):
    """Creates a backup of the goal file."""
    try:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backupfile = f"{goalfile}.{timestamp}.bak"
        os.rename(goalfile, backupfile)
        print(f"[BACKUP] Backed up existing goal file to: {os.path.basename(backupfile)}")
        logging.info(f"Backed up {goalfile} to {backupfile}")
    except Exception as e:
        print(f"[WARNING] Could not backup goal file: {e}")
        logging.warning(f"Could not backup goal file: {e}")

def generatecodeforstep(goalandworkflow: str, step: str) -> str:
    """Generates Python code for a workflow step."""
    prompt = f"""
    You are an expert Python developer.
    Given the following goal and workflow, write Python code for the following step:
    {goalandworkflow}
    Step: {step}

    IMPORTANT GUIDELINES:

Write functions that can be called programmatically, not interactive scripts
Avoid using input() statements in the main execution flow
If user input is needed, create functions that accept parameters instead
Focus on creating reusable, modular functions
Include example usage in comments, but don't execute interactive code in the main block
Ensure the code can run without user interaction for testing purposes
DO NOT include an 'if __name__ == "__main__":' block.


    Output ONLY valid Python code, nothing else.
    Ensure the code is a logical part of the overall program and can be combined with other steps.
    """

    # Use web search for better code generation
    code = safegemini(prompt, usewebsearch=True)
    if not code:
        logging.error(f"Failed to generate valid code for step: {step}")
        return None
    return sanitize_code(code)

def backupscript(scriptfile: str):
    """Creates a backup of the script."""
    try:
        if os.path.exists(scriptfile):
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backupfile = f"{scriptfile}.{timestamp}.bak"
            os.rename(scriptfile, backupfile)
            print(f"[BACKUP] Backed up existing script to: {os.path.basename(backupfile)}")
            logging.info(f"Backed up {scriptfile} to {backupfile}")
    except Exception as e:
        print(f"[WARNING] Could not backup script file: {e}")
        logging.warning(f"Could not backup script file: {e}")

def main():
    """Orchestrates project creation."""
    try:
        projectidea = getuserrequest()
        filename = generatefilename(projectidea)
        goalandworkflow = creategoalfile(filename, projectidea)
        if not goalandworkflow:
            logging.error("Failed to create goal file. Exiting.")
            return

        workflowsteps = [line.strip()[2:] for line in goalandworkflow.splitlines() if line.startswith("- ")]
        if len(workflowsteps) < 5:
            logging.error(f"Too few workflow steps: {len(workflowsteps)}")
            return

        finalcode = "# Generated Python script\n\n"
        scriptfile = f"{filename}.py"
        scriptfilepath = os.path.join(CURRENT_DIR, scriptfile)

        logging.info(f"Will create Python script: {scriptfilepath}")

        for i, step in enumerate(workflowsteps):
            logging.info(f"Generating code for step {i+1}: {step}")
            code = generatecodeforstep(goalandworkflow, step)
            if not code:
                logging.error(f"Failed to generate code for step {i+1}. Exiting.")
                return

            finalcode += f"# Step {i+1}: {step}\n{code}\n\n"

            logging.info(f"Step {i+1} code added successfully.")

        # Add main execution block
        finalcode += '\nif __name__ == "__main__":\n    main()\n'
        logging.info("[OK] Program generated successfully")

        # Validate final code before saving
        logging.info("\nValidating final code...")
        if isvalidpythoncode(finalcode):
            logging.info("[OK] Code validation successful!")
        else:
            logging.warning("[WARNING] Generated code may have syntax issues")

        # Save final script
        logging.info(f"\nSaving final script to: {scriptfilepath}")

        if os.path.exists(scriptfilepath):
            backupscript(scriptfilepath)

        try:
            with open(scriptfilepath, "w", encoding="utf-8") as f:
                f.write(finalcode)

            # Verify the file was created
            if os.path.exists(scriptfilepath):
                filesize = os.path.getsize(scriptfilepath)
                print(f"[OK] Script file created successfully!")

                # Show summary
                print(f"\n[SUCCESS] Project '{scriptfile}' created successfully!")
                print(f"File location: {scriptfilepath}")
                print(f"File size: {filesize} bytes ({len(finalcode)} characters)")
                print(f"Steps completed: {len(workflowsteps)}")
                print(f"To run your project: python {scriptfile}")

                logging.info(f"Project '{scriptfile}' created successfully at {scriptfilepath}")

                # List files in current directory for verification
                print(f"\nFiles in current directory ({CURRENT_DIR}):")
                try:
                    files = [f for f in os.listdir(CURRENT_DIR) if f.endswith(('.py', '.txt'))]
                    for file in sorted(files):
                        filepath = os.path.join(CURRENT_DIR, file)
                        size = os.path.getsize(filepath)
                        print(f"   {file} ({size} bytes)")
                except Exception as e:
                    print(f"   [ERROR] Could not list files: {e}")

            else:
                print(f"[ERROR] Failed to create script file: {scriptfile}")
                logging.error(f"Script file was not created at {scriptfilepath}")

        except Exception as e:
            print(f"[ERROR] Error saving script file: {e}")
            logging.error(f"Error saving script file: {e}")
            raise

    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        print(f"An error occurred: {e}. Exiting.")

if __name__ == "__main__":
    main()