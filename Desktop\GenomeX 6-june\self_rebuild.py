import ast
import brotli
import pickle
import reedsolo
import astor
import os
import json
import hashlib
import logging
from typing import Dict, Any, List
from io import BytesIO

# Set up logging
logging.basicConfig(level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s")

class GenomeX:
    """A self-regenerating system for codebases with super compression."""

    def __init__(self, codebase_path: str = "main.py", genome_path: str = "genome.bin"):
        self.codebase_path = codebase_path
        self.genome_path = genome_path
        self.rs = reedsolo.RSCodec(8)  # 8 bytes parity for ~4% overhead
        self.default_output_path = "restored_main.py"

    def _optimize_ast(self, tree: ast.AST) -> ast.AST:
        """Strip unnecessary AST attributes to reduce size."""
        for node in ast.walk(tree):
            for attr in ["lineno", "col_offset", "end_lineno", "end_col_offset", "ctx", "type_ignores"]:
                if hasattr(node, attr):
                    setattr(node, attr, None)
        return tree

    def _custom_ast_encode(self, tree: ast.AST) -> bytes:
        """Encode AST into a minimal binary format."""
        buffer = BytesIO()
        try:
            # Pickle with optimizations, simulating a custom node mapping
            pickle.dump(self._optimize_ast(tree), buffer, protocol=pickle.HIGHEST_PROTOCOL)
            return buffer.getvalue()
        except pickle.PicklingError as e:
            logging.error(f"Failed to encode AST: {e}")
            raise ValueError(f"Failed to encode AST: {e}")

    def _custom_ast_decode(self, data: bytes) -> ast.AST:
        """Decode binary AST back to AST object."""
        try:
            return pickle.load(BytesIO(data))
        except pickle.PicklingError as e:
            logging.error(f"Failed to decode AST: {e}")
            raise ValueError(f"Failed to decode AST: {e}")

    def _compress(self, data: bytes) -> bytes:
        """Compress data with Brotli."""
        try:
            return brotli.compress(data, quality=11, mode=brotli.MODE_TEXT)  # Max compression for text
        except brotli.error as e:
            logging.error(f"Compression failed: {e}")
            raise ValueError(f"Compression failed: {e}")

    def _decompress(self, data: bytes) -> bytes:
        """Decompress Brotli data."""
        try:
            return brotli.decompress(data)
        except brotli.error as e:
            logging.error(f"Decompression failed: {e}")
            raise ValueError(f"Decompression failed: {e}")

    def _parse_code_to_ast(self, code: str) -> ast.AST:
        """Parse source code to AST."""
        try:
            tree = ast.parse(code)
            return self._optimize_ast(tree)
        except SyntaxError as e:
            logging.error(f"Invalid syntax in codebase: {e}")
            raise ValueError(f"Invalid syntax in codebase: {e}")

    def _create_metadata(self, tree: ast.AST, filename: str) -> Dict[str, Any]:
        """Create minimal metadata with filename and truncated AST hash."""
        ast_data = self._custom_ast_encode(tree)
        full_hash = hashlib.sha256(ast_data).digest()
        metadata = {
            "filename": filename,
            "ast_hash": full_hash[:16].hex()  # 16-byte truncated hash
        }
        return metadata

    def create_genome(self):
        """Scan codebase and create genome."""
        if not os.path.exists(self.codebase_path):
            logging.error(f"Codebase not found at {self.codebase_path}")
            raise FileNotFoundError(f"Codebase not found at {self.codebase_path}")

        with open(self.codebase_path, "r", encoding="utf-8") as f:
            code = f.read()

        # Parse and encode AST
        tree = self._parse_code_to_ast(code)
        ast_data = self._custom_ast_encode(tree)
        
        # Create and compress metadata
        metadata = self._create_metadata(tree, os.path.basename(self.codebase_path))
        metadata_json = json.dumps(metadata, separators=(",", ":")).encode("utf-8")
        
        # Compress data
        ast_compressed = self._compress(ast_data)
        meta_compressed = self._compress(metadata_json)
        
        # Combine with length prefixes
        ast_len = len(ast_compressed).to_bytes(4, byteorder="big")
        meta_len = len(meta_compressed).to_bytes(4, byteorder="big")
        combined_data = ast_len + ast_compressed + meta_len + meta_compressed
        
        # Add Reed-Solomon encoding
        genome = self.rs.encode(combined_data)
        
        # Check size
        if len(genome) > 10 * 1024:
            logging.warning(f"Genome size {len(genome)} bytes exceeds 10 KB limit")
        
        with open(self.genome_path, "wb") as f:
            f.write(genome)
        logging.info(f"Genome created at {self.genome_path}, size: {len(genome)} bytes")
        print(f"Genome created at {self.genome_path}, size: {len(genome)} bytes")

    def _compare_asts(self, current_tree: ast.AST, original_tree: ast.AST) -> List[ast.AST]:
        """Identify missing nodes in current AST compared to original."""
        current_nodes = {(node.name, type(node).__name__) for node in current_tree.body 
                        if isinstance(node, (ast.FunctionDef, ast.ClassDef))}
        original_nodes = {(node.name, type(node).__name__) for node in original_tree.body 
                         if isinstance(node, (ast.FunctionDef, ast.ClassDef))}
        
        missing_nodes = []
        for node in original_tree.body:
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                if (node.name, type(node).__name__) not in current_nodes:
                    missing_nodes.append(node)
                    logging.info(f"Detected missing node: {node.name} ({type(node).__name__})")
        
        return missing_nodes

    def regenerate_code(self):
        """Rebuild codebase, restoring missing portions from genome."""
        if not os.path.exists(self.genome_path):
            logging.error(f"Genome not found at {self.genome_path}")
            raise FileNotFoundError(f"Genome not found at {self.genome_path}")

        with open(self.genome_path, "rb") as f:
            genome_data = f.read()

        # Decode Reed-Solomon
        try:
            combined_data = self.rs.decode(genome_data)[0]
        except reedsolo.ReedSolomonError as e:
            logging.error(f"Genome recovery failed: {e}")
            raise ValueError(f"Genome recovery failed: {e}")

        # Split data
        try:
            index = 0
            ast_len = int.from_bytes(combined_data[index:index+4], byteorder="big")
            index += 4
            ast_compressed = combined_data[index:index+ast_len]
            index += ast_len
            meta_len = int.from_bytes(combined_data[index:index+4], byteorder="big")
            index += 4
            meta_compressed = combined_data[index:index+meta_len]
        except IndexError as e:
            logging.error(f"Invalid genome structure: {e}")
            raise ValueError(f"Invalid genome structure: {e}")

        # Decompress
        ast_data = self._decompress(ast_compressed)
        metadata_json = self._decompress(meta_compressed)
        
        # Rebuild AST and metadata
        original_tree = self._custom_ast_decode(ast_data)
        try:
            metadata = json.loads(metadata_json.decode("utf-8"))
        except json.JSONDecodeError as e:
            logging.error(f"Failed to decode metadata: {e}")
            raise ValueError(f"Failed to decode metadata: {e}")
        
        # Verify AST integrity
        full_hash = hashlib.sha256(ast_data).digest()
        if metadata["ast_hash"] != full_hash[:16].hex():
            logging.error("AST hash mismatch; genome may be corrupted")
            raise ValueError("AST hash mismatch; genome may be corrupted")
        
        # Determine output path
        output_path = metadata.get("filename", self.default_output_path)
        if not output_path.endswith(".py"):
            output_path += ".py"

        if not os.path.exists(self.codebase_path):
            logging.info(f"Codebase missing; regenerating full file at {output_path}")
            regenerated_code = astor.to_source(original_tree)
        else:
            # Parse current codebase
            try:
                with open(self.codebase_path, "r", encoding="utf-8") as f:
                    current_code = f.read()
                current_tree = self._parse_code_to_ast(current_code)
            except (FileNotFoundError, ValueError) as e:
                logging.warning(f"Failed to parse current codebase: {e}; regenerating full file")
                regenerated_code = astor.to_source(original_tree)
                output_path = self.codebase_path
            else:
                # Compare ASTs and rebuild missing parts
                missing_nodes = self._compare_asts(current_tree, original_tree)
                if not missing_nodes:
                    logging.info("No missing nodes detected; codebase intact")
                    return
                
                # Merge missing nodes into current AST
                restored_tree = ast.Module(body=current_tree.body + missing_nodes, type_ignores=[])
                try:
                    regenerated_code = astor.to_source(restored_tree)
                except Exception as e:
                    logging.error(f"Failed to convert restored AST to code: {e}")
                    raise ValueError(f"Failed to convert restored AST to code: {e}")
                output_path = self.codebase_path
                logging.info(f"Restored {len(missing_nodes)} missing nodes")

        # Write regenerated code
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(regenerated_code)
        logging.info(f"Codebase regenerated at {output_path}")
        print(f"Codebase regenerated at {output_path}")

    def run(self):
        """Interactive menu for scan or rebuild."""
        print("\nGenomeX Self-Rebuild System")
        print("1. Scan Python file and create genome")
        print("2. Rebuild Python file from genome")
        choice = input("Choose an option (1 or 2): ").strip()

        if choice == "1":
            py_file = input("Enter the Python filename to compress (e.g., script.py): ").strip()
            if not os.path.isfile(py_file):
                print(f"File '{py_file}' does not exist.")
                return
            self.codebase_path = py_file
            self.genome_path = os.path.splitext(py_file)[0] + "_genome.bin"
            self.create_genome()

        elif choice == "2":
            bin_file = input("Enter the .bin filename to decompress (e.g., script.bin): ").strip()
            if not os.path.isfile(bin_file):
                print(f"File '{bin_file}' does not exist.")
                return
            self.genome_path = bin_file
            self.codebase_path = os.path.splitext(bin_file)[0] + ".py"
            self.regenerate_code()

        else:
            print("Invalid choice. Please enter 1 or 2.")

if __name__ == "__main__":
    GenomeX().run()