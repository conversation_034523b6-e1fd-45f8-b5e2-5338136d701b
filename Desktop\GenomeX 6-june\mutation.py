# new_evolve.py
import ast
import subprocess
import timeit
import random
import re
import logging
import time
from collections import defaultdict
from datetime import datetime
from libcst import parse_module, CSTTransformer, CSTVisitor, Break, For, Call, Attribute
from new_ai import gemini

# Configuration
MUTATION_RATIO = 0.8
MAX_VARIANTS = 1
GENERATIONS = 1
DSL_FILE = "mutations.dsl"
MAX_CHUNK_LINES = 2  # Process ~200 lines at once
GEMINI_RETRIES = 3

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
def calculate_score(code: str) -> float:
    """Calculate quality score for code using multiple metrics"""
    try:
        # Speed metric (execution time inverse)
        speed = 1 / (timeit.timeit(lambda: exec(code, {}), number=1) + 1e-6)
        
        # Correctness metric (successful executions)
        correctness = sum(run_safely(code) for _ in range(2)) / 2
        
        # Robustness metric (inverse of AST size)
        robustness = 1 / (len(parse_module(code).body) + 1)
        
        # Compactness metric (inverse of line count)
        compactness = 1 / (len(code.split('\n')) + 1)
        
        # Weighted total score
        return (0.4 * speed + 0.3 * correctness + 0.2 * robustness + 0.1 * compactness)
    except Exception as e:
        logging.warning(f"Score calculation failed: {str(e)}")
        return 0.0


class DSLCSTTransformer(CSTTransformer):
    def __init__(self, rules):
        self.rules = []
        for rule in rules:
            try:
                parsed_match = parse_module(rule['match'])
                parsed_replace = parse_module(rule['replace'])
                self.rules.append({
                    'name': rule['name'],
                    'match_ast': parsed_match,
                    'replace_ast': parsed_replace,
                    'conditions': rule.get('when', ''),
                    'weight': rule['weight']
                })
            except Exception as e:
                logging.error(f"Invalid rule '{rule['name']}': {str(e)}")
        self.current_rule = None
        self.applied = False

    def on_visit(self, node):
        if self.applied:
            return False
        for rule in self.rules:
            try:
                if node.deep_equals(rule['match_ast']):
                    if self.check_conditions(node, rule['conditions']):
                        self.current_rule = rule
                        self.applied = True
                        logging.debug(f"Applying rule: {rule['name']}")
                        return True
            except Exception as e:
                logging.error(f"Rule '{rule['name']}' error: {str(e)}")
                continue
        return False

    def check_conditions(self, node, conditions: str) -> bool:
        if 'single_append' in conditions:
            return self.has_single_append(node)
        if 'loop_has_break' in conditions:
            return not self.has_break_statement(node)
        return True

    def has_break_statement(self, node) -> bool:
        class BreakVisitor(CSTVisitor):
            def __init__(self):
                self.found = False
            def visit_Break(self, node):
                self.found = True
        visitor = BreakVisitor()
        node.visit(visitor)
        return visitor.found

    def has_single_append(self, node):
        class AppendVisitor(CSTVisitor):
            def __init__(self):
                self.append_count = 0
            def visit_Call(self, node):
                if isinstance(node.func, Attribute):
                    if node.func.attr.value == 'append':
                        self.append_count += 1
        visitor = AppendVisitor()
        if isinstance(node, For):
            node.body.visit(visitor)
        return visitor.append_count == 1

    def leave_For(self, original_node, updated_node):
        if self.current_rule:
            return self.current_rule['replace_ast'].body[0]
        return updated_node

def parse_dsl_rules(file_path: str) -> list:
    logging.info(f"Loading DSL rules from {file_path}")
    with open(file_path, 'r') as f:
        content = f.read()
    rules = []
    rule_pattern = r'rule "(.+?)" {\s*(.+?)\s*}(?=\s*rule|$)'
    rule_matches = list(re.finditer(rule_pattern, content, re.DOTALL))
    for match_idx, match in enumerate(rule_matches, 1):
        try:
            name = match.group(1)
            body = match.group(2)
            components = {
                'match': r'match:\s*"""(.*?)"""',
                'replace': r'replace:\s*"""(.*?)"""',
                'when': r'when:\s*{\s*(.+?)\s*}',
                'weight': r'weight:\s*([0-9.]+)'
            }
            rule = {'name': name, 'weight': 0.5}
            for key, pattern in components.items():
                found = re.search(pattern, body, re.DOTALL)
                if found:
                    value = found.group(1).strip()
                    if key in ['match', 'replace']:
                        value = re.sub(r'__PH_(\w+)', r'__PH_\1', value)
                    rule[key] = value
            if not all(rule.get(k) for k in ['match', 'replace']):
                raise ValueError(f"Missing required fields in rule {name}")
            rule['weight'] = float(rule.get('weight', 0.5))
            rules.append(rule)
        except Exception as e:
            logging.error(f"Failed to parse rule #{match_idx}: {str(e)}")
            continue
    logging.info(f"Successfully parsed {len(rules)} valid rules")
    return rules

def apply_dsl_rules(code: str, rules: list) -> str:
    try:
        transformer = DSLCSTTransformer(rules)
        modified_ast = parse_module(code).visit(transformer)
        return modified_ast.code
    except Exception as e:
        logging.error(f"DSL application failed: {str(e)}")
        return code

def validate_rule(rule: str) -> bool:
    try:
        required = ['match', 'replace', 'weight']
        return all(re.search(rf'{k}:', rule) for k in required)
    except:
        logging.warning("Invalid rule format detected")
        return False

def normalize_weights(rules: list):
    total = sum(r['weight'] for r in rules)
    if total == 0: return
    for rule in rules:
        rule['weight'] /= total

def run_safely(code: str) -> float:
    try:
        exec(code, {})
        return 1.0
    except:
        return 0.0

def chunk_code(code: str) -> list:
    """Split code into fixed-size chunks preserving line order"""
    lines = code.split('\n')
    return ['\n'.join(lines[i:i+MAX_CHUNK_LINES]) 
            for i in range(0, len(lines), MAX_CHUNK_LINES)]

def extract_code(response: str) -> str:
    """Extract code from Gemini response with multiple fallbacks"""
    # Try markdown code blocks first
    code_blocks = re.findall(r'```(?:python)?\n(.*?)\n```', response, re.DOTALL)
    if code_blocks:
        return '\n\n'.join(code_blocks)
    
    # Fallback to indented lines
    return '\n'.join([line for line in response.split('\n') 
                     if line.startswith((' ', '\t', 'def', 'class', '@'))])

def process_chunk(chunk: str, prompt: str) -> str:
    """Process a code chunk with retries and validation"""
    for attempt in range(GEMINI_RETRIES):
        try:
            response = gemini(f"{prompt}\n\n```python\n{chunk}\n```")
            cleaned = extract_code(response)
            parse_module(cleaned)  # Validate syntax
            return cleaned
        except Exception as e:
            logging.debug(f"Chunk processing attempt {attempt+1} failed: {str(e)}")
            time.sleep(1)
    return chunk  # Fallback to original

def call_gemini(prompt: str, code: str) -> str:
    """Process code in large chunks with minimal logging"""
    chunks = chunk_code(code)
    logging.info(f"Processing {len(chunks)} code chunks")
    
    processed = []
    for i, chunk in enumerate(chunks, 1):
        processed.append(process_chunk(chunk, prompt))
        logging.info(f"Processed chunk {i}/{len(chunks)}")
    
    return '\n'.join(processed)

def post_process(code: str) -> str:
    """Clean formatting and add docstrings"""
    try:
        # Format with Black first if available
        try:
            import black
            code = black.format_str(code, mode=black.Mode())
        except ImportError:
            pass
        
        # AI polishing with chunking
        prompt = """Return ONLY improved Python code with:
- Added docstrings
- PEP8 fixes
- Better variable names
- Preserved functionality
- No explanations"""
        
        return call_gemini(prompt, code)
    except Exception as e:
        logging.error(f"Post-processing failed: {e}")
        return code

def evolve_code(input_file: str, output_file: str):
    logging.info("🚀 Starting optimized evolution process")
    dsl_rules = parse_dsl_rules(DSL_FILE)
    
    # Load original code
    original_code = open(input_file).read()
    best_code = original_code
    best_score = calculate_score(original_code)
    logging.info(f"📊 Initial score: {best_score:.2f}")
    
    # Evolution parameters
    MAX_ATTEMPTS = 3
    IMPROVEMENT_FACTOR = 1.5 # 50% improvement required
    
    current_code = original_code
    current_score = best_score
    
    for attempt in range(1, MAX_ATTEMPTS+1):
        logging.info(f"\n⚡ Attempt {attempt}/{MAX_ATTEMPTS}")
        start_time = datetime.now()
        
        # Generate mutated version
        try:
            if random.random() < MUTATION_RATIO:
                variant = apply_dsl_rules(current_code, dsl_rules)
            else:
                variant = call_gemini(
                    "Improve this Python code focusing on critical performance aspects:",
                    current_code
                )
        except Exception as e:
            variant = current_code
        
        # Score the variant
        variant_score = calculate_score(variant)
        logging.info(f"🧪 Variant score: {variant_score:.2f} (Current: {current_score:.2f})")
        
        # Validate improvement
        if variant_score > (current_score * IMPROVEMENT_FACTOR):
            logging.info(f"🔥 Significant improvement found! Adopting changes")
            current_code = post_process(variant)
            current_score = calculate_score(current_code)
            
            # Update global best if better
            if current_score > best_score:
                best_code = current_code
                best_score = current_score
                logging.info(f"🏆 New best score: {best_score:.2f}")
        else:
            logging.info("🔄 Insufficient improvement - reverting to previous version")
            current_code = best_code  # Roll back to best known version
            current_score = best_score
        
        logging.info(f"⏱️ Attempt time: {(datetime.now() - start_time).total_seconds():.2f}s")
    
    # Save final best version
    logging.info(f"\n💾 Saving best version with score: {best_score:.2f}")
    with open(output_file, 'w') as f:
        f.write(best_code)
    
    logging.info("✅ Evolution complete!")

if __name__ == "__main__":
    evolve_code("main.py", "evolved_main.py")
